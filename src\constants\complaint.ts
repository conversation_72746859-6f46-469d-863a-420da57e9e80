/**
 * 投诉建议相关常量定义
 */

/** 投诉建议大类 */
export const ComplaintCategory = {
  投诉: 'complaint',
  建议: 'suggestion',
} as const;

/** 投诉建议小类 */
export const ComplaintSubCategory = {
  订单投诉: 'order',
  人员投诉: 'employee',
  平台建议: 'platform',
  服务建议: 'service',
  流程投诉: 'workflow',
} as const;

/** 投诉建议处理状态 */
export const ComplaintStatus = {
  待处理: 'pending',
  处理中: 'processing',
  已解决: 'resolved',
  已关闭: 'closed',
} as const;

/** 投诉建议来源类型 */
export const SourceType = {
  客户投诉建议: 'customer',
  员工建议: 'employee',
  管理员录入: 'admin',
  未知: 'unknown',
} as const;

/** 投诉建议大类选项 */
export const ComplaintCategoryOptions = [
  { label: '投诉', value: ComplaintCategory.投诉 },
  { label: '建议', value: ComplaintCategory.建议 },
];

/** 投诉建议小类选项 */
export const ComplaintSubCategoryOptions = [
  { label: '订单投诉', value: ComplaintSubCategory.订单投诉 },
  { label: '人员投诉', value: ComplaintSubCategory.人员投诉 },
  { label: '平台建议', value: ComplaintSubCategory.平台建议 },
  { label: '服务建议', value: ComplaintSubCategory.服务建议 },
  { label: '流程投诉', value: ComplaintSubCategory.流程投诉 },
];

/** 员工建议小类选项（只包含建议类型） */
export const EmployeeSuggestionSubCategoryOptions = [
  { label: '平台建议', value: ComplaintSubCategory.平台建议 },
  { label: '服务建议', value: ComplaintSubCategory.服务建议 },
  { label: '流程建议', value: ComplaintSubCategory.流程投诉 },
];

/** 投诉建议状态选项 */
export const ComplaintStatusOptions = [
  { label: '待处理', value: ComplaintStatus.待处理 },
  { label: '处理中', value: ComplaintStatus.处理中 },
  { label: '已解决', value: ComplaintStatus.已解决 },
  { label: '已关闭', value: ComplaintStatus.已关闭 },
];

/** 投诉建议来源类型选项 */
export const SourceTypeOptions = [
  { label: '客户投诉建议', value: SourceType.客户投诉建议 },
  { label: '员工建议', value: SourceType.员工建议 },
  { label: '管理员录入', value: SourceType.管理员录入 },
];

/** 状态配置 */
export const StatusConfig = {
  [ComplaintStatus.待处理]: { color: 'orange', text: '待处理' },
  [ComplaintStatus.处理中]: { color: 'blue', text: '处理中' },
  [ComplaintStatus.已解决]: { color: 'green', text: '已解决' },
  [ComplaintStatus.已关闭]: { color: 'gray', text: '已关闭' },
};

/** 分类配置 */
export const CategoryConfig = {
  [ComplaintCategory.投诉]: { color: 'red', text: '投诉' },
  [ComplaintCategory.建议]: { color: 'blue', text: '建议' },
};

/** 小类配置 */
export const SubCategoryConfig = {
  [ComplaintSubCategory.订单投诉]: '订单投诉',
  [ComplaintSubCategory.人员投诉]: '人员投诉',
  [ComplaintSubCategory.平台建议]: '平台建议',
  [ComplaintSubCategory.服务建议]: '服务建议',
  [ComplaintSubCategory.流程投诉]: '流程投诉',
};

/** 来源类型配置 */
export const SourceTypeConfig = {
  [SourceType.客户投诉建议]: {
    color: 'blue',
    text: '客户投诉建议',
    icon: '👤',
    description: undefined,
  },
  [SourceType.员工建议]: {
    color: 'green',
    text: '员工建议',
    icon: '👨‍💼',
    description: undefined,
  },
  [SourceType.管理员录入]: {
    color: 'orange',
    text: '管理员录入',
    icon: '👨‍💻',
    description: undefined,
  },
  [SourceType.未知]: {
    color: 'red',
    text: '未知',
    icon: '❓',
    description: '提交投诉或建议的账号可能已被删除'
  },
};
